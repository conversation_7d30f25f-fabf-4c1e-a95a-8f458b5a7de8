#!/usr/bin/env node
// 脚本：compress.js
// 功能：清理用户脚本的注释和空行，保留元数据，并格式化代码
// 用法：node compress.js -i input.js [-o output.user.js] [--no-format] [-v]

import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import generate from '@babel/generator'
import { parse } from '@babel/parser'
import traverse from '@babel/traverse'
import chalk from 'chalk'
import prettier from 'prettier'
import { hideBin } from 'yargs/helpers'
import yargs from 'yargs/yargs'

// 获取当前模块路径
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 命令行参数配置
const argv = yargs(hideBin(process.argv))
  .option('input', {
    alias: 'i',
    describe: '输入文件或目录路径',
    type: 'string',
    demandOption: true,
  })
  .option('output', {
    alias: 'o',
    describe: '输出文件或目录路径',
    type: 'string',
  })
  .option('no-format', {
    describe: '禁用 Prettier 格式化',
    type: 'boolean',
    default: false,
  })
  .option('verbose', {
    alias: 'v',
    describe: '显示详细处理信息',
    type: 'boolean',
    default: false,
  })
  .option('recursive', {
    alias: 'r',
    describe: '递归处理子目录',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .version('1.0.0')
  .alias('version', 'V')
  .epilogue('示例:\n  $ node compress.js -i src/edewakaru.js\n  $ node compress.js -i src -o dist -r')
  .argv

// 确保目录存在
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    if (argv.verbose) {
      console.log(chalk.gray(`  📁 创建目录: ${dirPath}`))
    }
  }
}

// 获取所有 JS 文件
function getAllJSFiles(dirPath, recursive = false) {
  const results = []

  function scanDirectory(currentPath) {
    const items = fs.readdirSync(currentPath)

    for (const item of items) {
      const fullPath = path.join(currentPath, item)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory() && recursive) {
        scanDirectory(fullPath)
      }
      else if (stat.isFile() && fullPath.endsWith('.js')) {
        results.push(fullPath)
      }
    }
  }

  scanDirectory(dirPath)
  return results
}

// 获取正确的输出路径
function getOutputPath(inputPath, outputBase) {
  const inputFile = path.basename(inputPath)
  const outputFile = inputFile.replace(/\.js$/, '.user.js')

  // 如果指定了输出路径
  if (outputBase) {
    // 如果输出路径是目录
    if (fs.existsSync(outputBase) && fs.statSync(outputBase).isDirectory()) {
      return path.join(outputBase, outputFile)
    }
    // 如果输出路径是文件
    return outputBase
  }

  // 默认输出到项目根目录的 dist 目录
  const rootDir = process.cwd()
  const outputDir = path.join(rootDir, 'dist')
  return path.join(outputDir, outputFile)
}

// 处理单个文件
async function processFile(inputPath, outputPath) {
  const startTime = Date.now()

  try {
    if (argv.verbose) {
      console.log(chalk.gray(`\n  🚧 开始处理: ${path.basename(inputPath)}`))
    }

    // 读取文件内容
    const code = fs.readFileSync(inputPath, 'utf8')
    const fileSize = Buffer.byteLength(code, 'utf8')

    // 提取元数据块
    const metadataMatch = code.match(/\/\/ ==UserScript==[\s\S]*?\/\/ ==\/UserScript==/)
    const metadata = metadataMatch ? metadataMatch[0] : ''
    const codeWithoutMetadata = metadata ? code.replace(metadata, '') : code

    // 使用 Babel 解析器创建 AST
    const ast = parse(codeWithoutMetadata, {
      sourceType: 'script',
      tokens: true,
      ranges: true,
      errorRecovery: true,
    })

    // 移除所有注释节点
    traverse.default(ast, {
      enter(path) {
        if (path.node.leadingComments)
          delete path.node.leadingComments
        if (path.node.trailingComments)
          delete path.node.trailingComments
        if (path.node.innerComments)
          delete path.node.innerComments
      },
    })

    // 生成代码
    const output = generate.default(
      ast,
      {
        retainLines: true,
        retainFunctionParens: true,
        comments: false,
        concise: false,
        jsescOption: { minimal: true },
      },
      codeWithoutMetadata,
    )

    // 删除空行
    const cleanedCode = output.code
      .split('\n')
      .filter(line => line.trim() !== '')
      .join('\n')

    // 使用 Prettier 格式化代码
    let formattedCode = cleanedCode
    if (!argv.noFormat) {
      formattedCode = await prettier.format(cleanedCode, {
        printWidth: 5000,
        singleQuote: true,
        semi: false,
        parser: 'babel',
      })
    }

    // 合并元数据和格式化后的代码
    const finalCode = `${metadata}\n\n${formattedCode}`

    // 确保输出目录存在
    ensureDirectoryExists(path.dirname(outputPath))

    // 写入输出文件
    fs.writeFileSync(outputPath, finalCode)

    // 计算处理结果
    const endTime = Date.now()
    const newSize = Buffer.byteLength(finalCode, 'utf8')
    const reduction = (((fileSize - newSize) / fileSize) * 100).toFixed(1)
    const timeTaken = endTime - startTime

    if (argv.verbose) {
      console.log(chalk.gray(`  ✅ 完成: ${path.basename(inputPath)}`))
      console.log(chalk.gray(`    ⏱️  处理时间: ${timeTaken}ms`))
      console.log(chalk.gray(`    📦 原始大小: ${(fileSize / 1024).toFixed(2)} KB`))
      console.log(chalk.gray(`    🗜️  优化大小: ${(newSize / 1024).toFixed(2)} KB`))
      console.log(chalk.gray(`    📉 体积减少: ${reduction}%`))
    }

    return {
      inputPath,
      outputPath,
      originalSize: fileSize,
      optimizedSize: newSize,
      reductionPercent: reduction,
      timeTaken,
    }
  }
  catch (error) {
    console.error(chalk.red(`  ❌ 处理失败: ${path.basename(inputPath)}`))
    console.error(chalk.red(`    💥 错误: ${error.message}`))

    if (argv.verbose) {
      console.error(chalk.red(`    🔍 堆栈: ${error.stack}`))
    }

    return {
      inputPath,
      error: true,
      message: error.message,
    }
  }
}

// 主处理函数
async function main() {
  const startTime = Date.now()

  try {
    console.log(chalk.cyan(`\n🚀 ${chalk.bold('用户脚本优化工具')}`))
    console.log(chalk.blue(`🔖 ${chalk.bold('版本:')} 1.0.0`))
    console.log(chalk.blue(`👤 ${chalk.bold('作者:')} iPumpkin`))
    console.log(chalk.gray('━'.repeat(60)))

    // 解析输入输出路径
    const inputPath = path.resolve(argv.input)
    const outputBase = argv.output ? path.resolve(argv.output) : null

    // 检查输入路径是否存在
    if (!fs.existsSync(inputPath)) {
      throw new Error(`输入路径不存在: ${inputPath}`)
    }

    // 处理单个文件
    if (fs.statSync(inputPath).isFile()) {
      const outputPath = getOutputPath(inputPath, outputBase)

      console.log(chalk.cyan(`📂 ${chalk.bold('输入文件:')} ${inputPath}`))
      console.log(chalk.cyan(`📂 ${chalk.bold('输出文件:')} ${outputPath}`))

      const result = await processFile(inputPath, outputPath)

      if (!result.error) {
        console.log(chalk.gray('━'.repeat(60)))
        console.log(chalk.green(`✅ ${chalk.bold('优化完成!')}`))
        console.log(chalk.cyan(`⏱️  ${chalk.bold('处理时间:')} ${result.timeTaken}ms`))
        console.log(chalk.cyan(`📦 ${chalk.bold('原始大小:')} ${(result.originalSize / 1024).toFixed(2)} KB`))
        console.log(chalk.cyan(`🗜️  ${chalk.bold('优化大小:')} ${(result.optimizedSize / 1024).toFixed(2)} KB`))
        console.log(chalk.green(`📉 ${chalk.bold('体积减少:')} ${result.reductionPercent}%`))
      }
    }
    // 处理目录
    else {
      const inputDir = inputPath
      const outputDir = outputBase || path.join(inputDir, '..', 'dist')

      console.log(chalk.cyan(`📁 ${chalk.bold('输入目录:')} ${inputDir}`))
      console.log(chalk.cyan(`📁 ${chalk.bold('输出目录:')} ${outputDir}`))

      // 确保输出目录存在
      ensureDirectoryExists(outputDir)

      // 获取所有 JS 文件
      const jsFiles = getAllJSFiles(inputDir, argv.recursive)

      if (jsFiles.length === 0) {
        console.log(chalk.yellow('⚠️  未找到任何 JS 文件'))
        return
      }

      console.log(chalk.cyan(`📄 ${chalk.bold('找到文件:')} ${jsFiles.length} 个`))

      // 处理所有文件
      const results = []
      for (const file of jsFiles) {
        const outputFile = getOutputPath(file, outputDir)
        results.push(await processFile(file, outputFile))
      }

      // 计算汇总信息
      const endTime = Date.now()
      const totalTime = endTime - startTime

      const successResults = results.filter(r => !r.error)
      const errorResults = results.filter(r => r.error)

      const totalOriginalSize = successResults.reduce((sum, r) => sum + r.originalSize, 0)
      const totalOptimizedSize = successResults.reduce((sum, r) => sum + r.optimizedSize, 0)
      const totalReduction = (((totalOriginalSize - totalOptimizedSize) / totalOriginalSize) * 100).toFixed(1)

      console.log(chalk.gray('━'.repeat(60)))
      console.log(chalk.green(`✅ ${chalk.bold('批量处理完成!')}`))
      console.log(chalk.cyan(`⏱️  ${chalk.bold('总处理时间:')} ${totalTime}ms`))
      console.log(chalk.cyan(`📦 ${chalk.bold('总原始大小:')} ${(totalOriginalSize / 1024).toFixed(2)} KB`))
      console.log(chalk.cyan(`🗜️  ${chalk.bold('总优化大小:')} ${(totalOptimizedSize / 1024).toFixed(2)} KB`))
      console.log(chalk.green(`📉 ${chalk.bold('总体积减少:')} ${totalReduction}%`))
      console.log(chalk.cyan(`📄 ${chalk.bold('处理文件:')} ${successResults.length} 个成功, ${errorResults.length} 个失败`))

      if (errorResults.length > 0) {
        console.log(chalk.yellow(`⚠️  ${chalk.bold('失败文件:')}`))
        for (const result of errorResults) {
          console.log(chalk.yellow(`    - ${path.basename(result.inputPath)}: ${result.message}`))
        }
      }
    }
  }
  catch (error) {
    console.log(chalk.gray('━'.repeat(60)))
    console.error(chalk.red(`❌ ${chalk.bold('处理失败!')}`))
    console.error(chalk.red(`  💥 错误: ${error.message}`))

    if (argv.verbose) {
      console.error(chalk.red(`  🔍 堆栈: ${error.stack}`))
    }

    process.exit(1)
  }
}

// 执行主函数
main()
